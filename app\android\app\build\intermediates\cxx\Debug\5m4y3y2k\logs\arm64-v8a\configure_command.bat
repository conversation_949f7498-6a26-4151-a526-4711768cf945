@echo off
"C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HK:\\2025\\thenextdoor\\app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=arm64-v8a" ^
  "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a" ^
  "-DANDROID_NDK=C:\\Android\\android-sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_ANDROID_NDK=C:\\Android\\android-sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_TOOLCHAIN_FILE=C:\\Android\\android-sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake" ^
  "-DC<PERSON><PERSON>_MAKE_PROGRAM=C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=K:\\2025\\thenextdoor\\app\\android\\app\\build\\intermediates\\cxx\\Debug\\5m4y3y2k\\obj\\arm64-v8a" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=K:\\2025\\thenextdoor\\app\\android\\app\\build\\intermediates\\cxx\\Debug\\5m4y3y2k\\obj\\arm64-v8a" ^
  "-DCMAKE_BUILD_TYPE=Debug" ^
  "-DCMAKE_FIND_ROOT_PATH=K:\\2025\\thenextdoor\\app\\android\\app\\.cxx\\Debug\\5m4y3y2k\\prefab\\arm64-v8a\\prefab" ^
  "-BK:\\2025\\thenextdoor\\app\\android\\app\\.cxx\\Debug\\5m4y3y2k\\arm64-v8a" ^
  -GNinja ^
  "-DPROJECT_BUILD_DIR=K:\\2025\\thenextdoor\\app\\android\\app\\build" ^
  "-DPROJECT_ROOT_DIR=K:\\2025\\thenextdoor\\app\\android" ^
  "-DREACT_ANDROID_DIR=K:\\2025\\thenextdoor\\app\\node_modules\\react-native\\ReactAndroid" ^
  "-DANDROID_STL=c++_shared" ^
  "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"
