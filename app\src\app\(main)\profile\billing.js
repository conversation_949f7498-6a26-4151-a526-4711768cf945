import React, { useState, useEffect } from "react";
import {
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
} from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "../../../shared/context/ThemeContext";
import { useSubscription } from "../../../shared/hooks/useSubscription";
import SafeAreaWrapper from "../../../shared/components/SafeAreaWrapper";
import { subscriptionService } from "../../../shared/services/subscriptionService";

// Import modern components
import {
  Container,
  Row,
  Column,
  Spacer,
  Text,
  Heading,
  ModernCard,
  ModernButton,
  ModernErrorMessage,
} from "../../../shared/components";

/**
 * Billing page component
 * Shows billing details, next payment date, and subscription management
 */
const BillingPage = () => {
  const router = useRouter();
  const { theme } = useTheme();
  
  // Subscription hook
  const {
    currentPlan,
    hasActiveSubscription,
    loading: subscriptionLoading,
    refreshSubscription,
  } = useSubscription();

  // State
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [billingDetails, setBillingDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch billing details
  const fetchBillingDetails = async () => {
    try {
      setError(null);
      console.log('🔄 Fetching billing details...');

      const response = await subscriptionService.getBillingDetails();

      if (response.success && response.data) {
        const { subscription, latestTransaction, autoRenewal } = response.data;

        if (subscription) {
          setBillingDetails({
            nextBillingDate: subscription.nextBillingDate ? new Date(subscription.nextBillingDate) : null,
            amount: subscription.price || 0,
            currency: subscription.currency || 'INR',
            paymentMethod: latestTransaction?.razorpayPaymentId
              ? `Payment ID ending in ${latestTransaction.razorpayPaymentId.slice(-4)}`
              : 'Razorpay Payment',
            autoRenewal: autoRenewal !== false,
            planName: subscription.planName || `${subscription.planType} ${subscription.planDuration}`,
            status: subscription.status,
          });
          console.log('✅ Billing details loaded successfully');
        } else {
          console.log('ℹ️ No active subscription found');
          setBillingDetails(null);
        }
      } else {
        console.warn('⚠️ No billing data received');
        setBillingDetails(null);
      }
    } catch (err) {
      console.error('❌ Error fetching billing details:', err);
      setError('Failed to load billing details');
      setBillingDetails(null);
    } finally {
      setLoading(false);
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await Promise.all([
      refreshSubscription(),
      fetchBillingDetails(),
    ]);
    setIsRefreshing(false);
  };

  // Handle auto-renewal toggle
  const handleAutoRenewalToggle = () => {
    Alert.alert(
      "Auto-Renewal",
      "Auto-renewal settings can be managed through your subscription provider.",
      [{ text: "OK" }]
    );
  };

  // Handle payment method update
  const handleUpdatePaymentMethod = () => {
    Alert.alert(
      "Update Payment Method",
      "Payment method updates are handled through your subscription provider.",
      [{ text: "OK" }]
    );
  };

  useEffect(() => {
    fetchBillingDetails();
  }, [hasActiveSubscription, currentPlan?.id, currentPlan?.tier]);

  if (subscriptionLoading || loading) {
    return (
      <SafeAreaWrapper>
        <Container withPadding>
          <Column align="center" justify="center" style={{ flex: 1 }}>
            <ActivityIndicator size="large" color={theme.colors.primary[500]} />
            <Spacer size="md" />
            <Text>Loading billing details...</Text>
          </Column>
        </Container>
      </SafeAreaWrapper>
    );
  }

  if (!hasActiveSubscription) {
    return (
      <SafeAreaWrapper>
        <Container withPadding>
          <Row align="center" style={{ marginBottom: theme.spacing.lg }}>
            <TouchableOpacity
              onPress={() => router.back()}
              style={{ marginRight: theme.spacing.md }}
            >
              <Ionicons
                name="arrow-back"
                size={24}
                color={theme.colors.brandNavy}
              />
            </TouchableOpacity>
            <Heading level="h2" style={{ color: theme.colors.brandNavy }}>
              Billing
            </Heading>
          </Row>

          <Column align="center" justify="center" style={{ flex: 1 }}>
            <Ionicons
              name="card-outline"
              size={64}
              color={theme.colors.neutral[400]}
            />
            <Spacer size="lg" />
            <Heading level="h3" style={{ color: theme.colors.brandNavy, textAlign: 'center' }}>
              No Active Subscription
            </Heading>
            <Spacer size="sm" />
            <Text style={{ color: theme.colors.neutral[600], textAlign: 'center' }}>
              You don't have an active subscription. Subscribe to access billing details.
            </Text>
            <Spacer size="lg" />
            <ModernButton
              title="View Plans"
              onPress={() => router.push("/subscription")}
              variant="primary"
            />
          </Column>
        </Container>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <Container>
        <ScrollView
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.brandGreen]}
              tintColor={theme.colors.brandGreen}
            />
          }
        >
          {/* Header */}
          <View style={{ padding: theme.spacing.md }}>
            <Row align="center" style={{ marginBottom: theme.spacing.lg }}>
              <TouchableOpacity
                onPress={() => router.back()}
                style={{ marginRight: theme.spacing.md }}
              >
                <Ionicons
                  name="arrow-back"
                  size={24}
                  color={theme.colors.brandNavy}
                />
              </TouchableOpacity>
              <Heading level="h2" style={{ color: theme.colors.brandNavy }}>
                Billing
              </Heading>
            </Row>
          </View>

          {error && (
            <View style={{ paddingHorizontal: theme.spacing.md }}>
              <ModernErrorMessage message={error} />
              <Spacer size="md" />
            </View>
          )}

          {/* Current Plan */}
          <View style={{ paddingHorizontal: theme.spacing.md }}>
            <ModernCard
              style={{
                backgroundColor: theme.colors.brandWhite,
                borderRadius: 16,
                padding: theme.spacing.lg,
              }}
            >
              <Text
                variant="caption"
                weight="medium"
                style={{
                  color: theme.colors.neutral[600],
                  marginBottom: 8,
                }}
              >
                CURRENT PLAN
              </Text>
              <Heading
                level="h3"
                style={{
                  color: theme.colors.brandNavy,
                  marginBottom: theme.spacing.sm,
                }}
              >
                {currentPlan.name}
              </Heading>
              <Text style={{ color: theme.colors.neutral[600] }}>
                {currentPlan.description}
              </Text>
            </ModernCard>
          </View>

          <Spacer size="md" />

          {/* Billing Details */}
          {billingDetails && (
            <View style={{ paddingHorizontal: theme.spacing.md }}>
              <ModernCard
                style={{
                  backgroundColor: theme.colors.brandWhite,
                  borderRadius: 16,
                  padding: theme.spacing.lg,
                }}
              >
                <Text
                  variant="caption"
                  weight="medium"
                  style={{
                    color: theme.colors.neutral[600],
                    marginBottom: 8,
                  }}
                >
                  BILLING DETAILS
                </Text>
                <Heading
                  level="h3"
                  style={{
                    color: theme.colors.brandNavy,
                    marginBottom: theme.spacing.md,
                  }}
                >
                  Next Payment
                </Heading>

                {/* Next Billing Date */}
                <Row
                  justify="space-between"
                  align="center"
                  style={{
                    paddingVertical: 12,
                    borderBottomWidth: 1,
                    borderBottomColor: theme.colors.neutral[100],
                  }}
                >
                  <Text weight="medium" style={{ color: theme.colors.brandNavy }}>
                    Next billing date
                  </Text>
                  <Text style={{ color: theme.colors.neutral[600] }}>
                    {new Date(billingDetails.nextBillingDate).toLocaleDateString()}
                  </Text>
                </Row>

                {/* Amount */}
                <Row
                  justify="space-between"
                  align="center"
                  style={{
                    paddingVertical: 12,
                    borderBottomWidth: 1,
                    borderBottomColor: theme.colors.neutral[100],
                  }}
                >
                  <Text weight="medium" style={{ color: theme.colors.brandNavy }}>
                    Amount
                  </Text>
                  <Text style={{ color: theme.colors.neutral[600] }}>
                    {billingDetails.currency === 'INR' ? '₹' : '$'}{billingDetails.amount}
                  </Text>
                </Row>

                {/* Payment Method */}
                <Row
                  justify="space-between"
                  align="center"
                  style={{ paddingVertical: 12 }}
                >
                  <Text weight="medium" style={{ color: theme.colors.brandNavy }}>
                    Payment method
                  </Text>
                  <TouchableOpacity onPress={handleUpdatePaymentMethod}>
                    <Row align="center">
                      <Text style={{ color: theme.colors.neutral[600], marginRight: 4 }}>
                        {billingDetails.paymentMethod}
                      </Text>
                      <Ionicons
                        name="chevron-forward"
                        size={16}
                        color={theme.colors.neutral[400]}
                      />
                    </Row>
                  </TouchableOpacity>
                </Row>
              </ModernCard>
            </View>
          )}

          <Spacer size="md" />

          {/* Actions */}
          <View style={{ paddingHorizontal: theme.spacing.md }}>
            <ModernButton
              title="Manage Subscription"
              onPress={() => router.push("/subscription")}
              variant="outline"
              style={{ marginBottom: theme.spacing.sm }}
            />
            <ModernButton
              title="View Transactions"
              onPress={() => router.push("/profile/transactions")}
              variant="outline"
            />
          </View>

          {/* Bottom Padding for Floating Tab Bar */}
          <View style={{ height: 100 }} />
        </ScrollView>
      </Container>
    </SafeAreaWrapper>
  );
};

export default BillingPage;
