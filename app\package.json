{"name": "app", "version": "1.0.0", "main": "index.js", "expo": {"scheme": "UNextDoor", "doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}, "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "preinstall": "npm config set legacy-peer-deps true"}, "dependencies": {"@config-plugins/react-native-webrtc": "^10.0.0", "@expo/config-plugins": "^10.0.0", "@expo/metro-runtime": "^5.0.4", "@gluestack-style/react": "^1.0.57", "@gluestack-ui/config": "^1.1.20", "@gluestack-ui/themed": "^1.1.73", "@hookform/resolvers": "^5.0.1", "@lottiefiles/dotlottie-react": "^0.13.5", "@openai/realtime-api-beta": "github:openai/openai-realtime-api-beta", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "^11.4.1", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.9.0", "base-64": "^1.0.0", "event-target-shim": "^6.0.2", "expo": "53.0.13", "expo-audio": "~0.4.7", "expo-av": "~15.1.6", "expo-battery": "~9.1.4", "expo-blur": "~14.1.5", "expo-brightness": "^13.1.4", "expo-build-properties": "~0.14.6", "expo-camera": "~16.1.9", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.1", "expo-device": "^7.1.4", "expo-file-system": "~18.1.0", "expo-font": "^13.3.1", "expo-haptics": "~14.1.4", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "^7.1.5", "expo-media-library": "~17.1.7", "expo-network": "^7.1.5", "expo-notifications": "~0.31.3", "expo-router": "~5.1.1", "expo-secure-store": "^14.2.3", "expo-speech": "^13.1.7", "expo-status-bar": "^2.2.3", "expo-system-ui": "~5.0.9", "expo-video": "~2.2.2", "lottie-react-native": "^7.2.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-native": "0.79.4", "react-native-confetti-cannon": "^1.5.2", "react-native-gesture-handler": "^2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-incall-manager": "^4.2.1", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "^3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webrtc": "^124.0.5", "react-native-webrtc-web-shim": "^1.0.7", "react-native-webview": "^13.15.0", "react-redux": "^9.2.0", "redux": "^5.0.1", "socket.io-client": "^4.8.1", "zod": "^3.24.4"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.23.3", "@expo/cli": "^0.24.13", "@expo/prebuild-config": "^9.0.0", "@react-native-community/cli": "^18.0.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.4.3", "jest": "^29.7.0", "jest-expo": "~53.0.7", "react-test-renderer": "^19.0.0"}, "jest": {"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg|@gluestack-ui/.*)"], "setupFilesAfterEnv": ["./jest.setup.js"], "collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/**/*.test.{js,jsx}", "!src/app/**/*", "!**/node_modules/**"]}, "private": true}