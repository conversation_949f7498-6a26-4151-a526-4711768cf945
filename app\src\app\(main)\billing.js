import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  StyleSheet, 
  SafeAreaView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Switch,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useAuth } from '../../features/auth/context/AuthContext';
import { SubscriptionService } from '../../shared/services/subscriptionService';

const BillingScreen = () => {
  const [loading, setLoading] = useState(false);
  const [autoRenewal, setAutoRenewal] = useState(true);
  const [cancelModalVisible, setCancelModalVisible] = useState(false);
  const [billingDetails, setBillingDetails] = useState(null);
  const [subscription, setSubscription] = useState(null);
  const [subscriptionLoading, setSubscriptionLoading] = useState(true);
  
  const { user, token } = useAuth();
  const router = useRouter();

  useEffect(() => {
    fetchSubscriptionAndBillingData();
  }, []);

  const fetchSubscriptionAndBillingData = async () => {
    try {
      setSubscriptionLoading(true);

      // Fetch current subscription
      const subscriptionData = await SubscriptionService.getCurrentSubscription();
      if (subscriptionData.success) {
        setSubscription(subscriptionData.data.subscription);
      }

      // Fetch billing details
      const billingData = await SubscriptionService.getBillingDetails();
      if (billingData.success) {
        setBillingDetails(billingData.data);
        setAutoRenewal(billingData.data.autoRenewal !== false);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      Alert.alert('Error', 'Failed to load billing information');
    } finally {
      setSubscriptionLoading(false);
    }
  };

  const refreshSubscription = () => {
    fetchSubscriptionAndBillingData();
  };

  const handleCancelSubscription = async () => {
    try {
      setLoading(true);
      const data = await SubscriptionService.cancelSubscription();
      
      if (data.success) {
        Alert.alert(
          'Subscription Cancelled',
          'Your subscription has been cancelled. You can continue using the premium features until the end of your current billing period.',
          [{ text: 'OK', onPress: () => {
            setCancelModalVisible(false);
            refreshSubscription();
          }}]
        );
      } else {
        throw new Error(data.message || 'Failed to cancel subscription');
      }
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      Alert.alert(
        'Error',
        'Failed to cancel subscription. Please try again or contact support.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const toggleAutoRenewal = async (value) => {
    try {
      setLoading(true);
      const data = await SubscriptionService.updateAutoRenewal(value);
      
      if (data.success) {
        setAutoRenewal(value);
        Alert.alert(
          'Auto-Renewal Updated',
          value ? 'Your subscription will automatically renew.' : 'Auto-renewal has been disabled.',
          [{ text: 'OK' }]
        );
      } else {
        throw new Error(data.message || 'Failed to update auto-renewal');
      }
    } catch (error) {
      console.error('Error updating auto-renewal:', error);
      Alert.alert(
        'Error',
        'Failed to update auto-renewal setting. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatAmount = (amount, currency) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency?.toUpperCase() || 'USD',
    }).format(amount / 100);
  };

  const getPlanColor = (planName) => {
    switch (planName?.toLowerCase()) {
      case 'basic':
        return '#10B981';
      case 'standard':
        return '#3B82F6';
      case 'pro':
        return '#8B5CF6';
      default:
        return '#6B7280';
    }
  };

  const renderSubscriptionInfo = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Current Subscription</Text>
      
      <View style={styles.card}>
        <View style={styles.planHeader}>
          <View style={[styles.planBadge, { backgroundColor: getPlanColor(subscription?.planName) + '20' }]}>
            <Text style={[styles.planName, { color: getPlanColor(subscription?.planName) }]}>
              {subscription?.planName || 'Free'}
            </Text>
          </View>
          <Text style={styles.planStatus}>
            {subscription?.status === 'cancelled' ? 'Cancelled' : 'Active'}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Monthly Price</Text>
          <Text style={styles.infoValue}>
            {subscription?.amount ? formatAmount(subscription.amount, subscription.currency) : 'Free'}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Next Billing Date</Text>
          <Text style={styles.infoValue}>
            {formatDate(subscription?.nextBillingDate)}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Subscription Started</Text>
          <Text style={styles.infoValue}>
            {formatDate(subscription?.startDate)}
          </Text>
        </View>
        
        {subscription?.endDate && (
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>
              {subscription?.status === 'cancelled' ? 'Access Until' : 'End Date'}
            </Text>
            <Text style={styles.infoValue}>
              {formatDate(subscription?.endDate)}
            </Text>
          </View>
        )}
      </View>
    </View>
  );

  const renderBillingSettings = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Billing Settings</Text>
      
      <View style={styles.card}>
        <View style={styles.settingRow}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingTitle}>Auto-Renewal</Text>
            <Text style={styles.settingDescription}>
              Automatically renew your subscription each month
            </Text>
          </View>
          <Switch
            value={autoRenewal}
            onValueChange={toggleAutoRenewal}
            trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
            thumbColor={autoRenewal ? '#FFFFFF' : '#FFFFFF'}
            disabled={loading || subscription?.status === 'cancelled'}
          />
        </View>
      </View>
    </View>
  );

  const renderPaymentMethods = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Payment Methods</Text>
      
      <TouchableOpacity 
        style={styles.card}
        onPress={() => {
          Alert.alert(
            'Payment Methods',
            'Payment methods are managed through Razorpay. You can update your payment method during your next payment.',
            [{ text: 'OK' }]
          );
        }}
      >
        <View style={styles.paymentMethodRow}>
          <View style={styles.paymentMethodIcon}>
            <Ionicons name="card" size={24} color="#4F46E5" />
          </View>
          <View style={styles.paymentMethodInfo}>
            <Text style={styles.paymentMethodTitle}>Razorpay</Text>
            <Text style={styles.paymentMethodDescription}>
              Secure payment processing
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
        </View>
      </TouchableOpacity>
    </View>
  );

  const renderActions = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Actions</Text>
      
      <View style={styles.actionsCard}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => router.push('/subscription')}
        >
          <Ionicons name="arrow-up-circle" size={24} color="#10B981" />
          <Text style={[styles.actionText, { color: '#10B981' }]}>
            Upgrade Plan
          </Text>
          <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
        </TouchableOpacity>
        
        <View style={styles.actionDivider} />
        
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => router.push('/transactions')}
        >
          <Ionicons name="receipt" size={24} color="#3B82F6" />
          <Text style={[styles.actionText, { color: '#3B82F6' }]}>
            Transaction History
          </Text>
          <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
        </TouchableOpacity>
        
        {subscription?.planName && subscription?.planName !== 'Free' && subscription?.status !== 'cancelled' && (
          <>
            <View style={styles.actionDivider} />
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => setCancelModalVisible(true)}
            >
              <Ionicons name="close-circle" size={24} color="#EF4444" />
              <Text style={[styles.actionText, { color: '#EF4444' }]}>
                Cancel Subscription
              </Text>
              <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
            </TouchableOpacity>
          </>
        )}
      </View>
    </View>
  );

  const renderCancelModal = () => (
    <Modal
      visible={cancelModalVisible}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setCancelModalVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Ionicons name="warning" size={48} color="#EF4444" />
            <Text style={styles.modalTitle}>Cancel Subscription?</Text>
          </View>
          
          <Text style={styles.modalText}>
            Are you sure you want to cancel your subscription? You'll continue to have access to premium features until {formatDate(subscription?.nextBillingDate)}.
          </Text>
          
          <View style={styles.modalButtons}>
            <TouchableOpacity 
              style={[styles.modalButton, styles.cancelButton]}
              onPress={() => setCancelModalVisible(false)}
            >
              <Text style={styles.cancelButtonText}>Keep Subscription</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.modalButton, styles.confirmButton]}
              onPress={handleCancelSubscription}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.confirmButtonText}>Cancel Subscription</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  if (subscriptionLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2']}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity 
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Billing & Payments</Text>
          </View>
        </LinearGradient>
        
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4F46E5" />
          <Text style={styles.loadingText}>Loading billing information...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Billing & Payments</Text>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderSubscriptionInfo()}
        {renderBillingSettings()}
        {renderPaymentMethods()}
        {renderActions()}
      </ScrollView>

      {renderCancelModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 16,
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  planBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  planName: {
    fontSize: 14,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  planStatus: {
    fontSize: 14,
    color: '#6B7280',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  infoLabel: {
    fontSize: 14,
    color: '#6B7280',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#111827',
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
  paymentMethodRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentMethodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#EEF2FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  paymentMethodInfo: {
    flex: 1,
  },
  paymentMethodTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 4,
  },
  paymentMethodDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
  actionsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  actionText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
  },
  actionDivider: {
    height: 1,
    backgroundColor: '#F3F4F6',
    marginHorizontal: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginTop: 16,
  },
  modalText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#F3F4F6',
  },
  confirmButton: {
    backgroundColor: '#EF4444',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

export default BillingScreen;
