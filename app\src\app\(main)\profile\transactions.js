import React, { useState, useEffect } from "react";
import {
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  FlatList,
} from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "../../../shared/context/ThemeContext";
import { useSubscription } from "../../../shared/hooks/useSubscription";
import SafeAreaWrapper from "../../../shared/components/SafeAreaWrapper";

// Import modern components
import {
  Container,
  Row,
  Column,
  Spacer,
  Text,
  Heading,
  ModernCard,
  ModernErrorMessage,
} from "../../../shared/components";

/**
 * Transactions page component
 * Shows transaction history with dates, amounts, and status
 */
const TransactionsPage = () => {
  const router = useRouter();
  const { theme } = useTheme();
  
  // Subscription hook
  const {
    hasActiveSubscription,
    loading: subscriptionLoading,
    refreshSubscription,
  } = useSubscription();

  // State
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch transactions
  const fetchTransactions = async () => {
    try {
      setError(null);
      // TODO: Implement API call to fetch transaction history
      // For now, use mock data
      const mockTransactions = [
        {
          id: '1',
          date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          amount: 299,
          currency: 'INR',
          status: 'completed',
          description: 'Standard Quarterly Subscription',
          paymentMethod: 'Card ending in ****1234',
        },
        {
          id: '2',
          date: new Date(Date.now() - 37 * 24 * 60 * 60 * 1000),
          amount: 99,
          currency: 'INR',
          status: 'completed',
          description: 'Basic Monthly Subscription',
          paymentMethod: 'Card ending in ****1234',
        },
        {
          id: '3',
          date: new Date(Date.now() - 67 * 24 * 60 * 60 * 1000),
          amount: 99,
          currency: 'INR',
          status: 'failed',
          description: 'Basic Monthly Subscription',
          paymentMethod: 'Card ending in ****5678',
        },
      ];
      
      setTransactions(mockTransactions);
    } catch (err) {
      console.error('Error fetching transactions:', err);
      setError('Failed to load transaction history');
    } finally {
      setLoading(false);
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await Promise.all([
      refreshSubscription(),
      fetchTransactions(),
    ]);
    setIsRefreshing(false);
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return theme.colors.brandGreen;
      case 'failed':
        return theme.colors.error[500];
      case 'pending':
        return theme.colors.warning.main;
      default:
        return theme.colors.neutral[500];
    }
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return 'checkmark-circle';
      case 'failed':
        return 'close-circle';
      case 'pending':
        return 'time';
      default:
        return 'help-circle';
    }
  };

  // Render transaction item
  const renderTransaction = ({ item }) => (
    <ModernCard
      style={{
        backgroundColor: theme.colors.brandWhite,
        borderRadius: 12,
        padding: theme.spacing.md,
        marginBottom: theme.spacing.sm,
      }}
    >
      <Row justify="space-between" align="flex-start">
        <Column style={{ flex: 1 }}>
          <Text
            weight="medium"
            style={{
              color: theme.colors.brandNavy,
              marginBottom: 4,
            }}
          >
            {item.description}
          </Text>
          <Text
            variant="caption"
            style={{
              color: theme.colors.neutral[600],
              marginBottom: 4,
            }}
          >
            {item.date.toLocaleDateString()} • {item.paymentMethod}
          </Text>
          <Row align="center">
            <Ionicons
              name={getStatusIcon(item.status)}
              size={14}
              color={getStatusColor(item.status)}
              style={{ marginRight: 4 }}
            />
            <Text
              variant="caption"
              weight="medium"
              style={{
                color: getStatusColor(item.status),
                textTransform: 'capitalize',
              }}
            >
              {item.status}
            </Text>
          </Row>
        </Column>
        
        <Column align="flex-end">
          <Text
            weight="semibold"
            style={{
              color: theme.colors.brandNavy,
              fontSize: 16,
            }}
          >
            {item.currency === 'INR' ? '₹' : '$'}{item.amount}
          </Text>
        </Column>
      </Row>
    </ModernCard>
  );

  useEffect(() => {
    fetchTransactions();
  }, []);

  if (subscriptionLoading || loading) {
    return (
      <SafeAreaWrapper>
        <Container withPadding>
          <Column align="center" justify="center" style={{ flex: 1 }}>
            <ActivityIndicator size="large" color={theme.colors.primary[500]} />
            <Spacer size="md" />
            <Text>Loading transactions...</Text>
          </Column>
        </Container>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <Container>
        <ScrollView
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.brandGreen]}
              tintColor={theme.colors.brandGreen}
            />
          }
        >
          {/* Header */}
          <View style={{ padding: theme.spacing.md }}>
            <Row align="center" style={{ marginBottom: theme.spacing.lg }}>
              <TouchableOpacity
                onPress={() => router.back()}
                style={{ marginRight: theme.spacing.md }}
              >
                <Ionicons
                  name="arrow-back"
                  size={24}
                  color={theme.colors.brandNavy}
                />
              </TouchableOpacity>
              <Heading level="h2" style={{ color: theme.colors.brandNavy }}>
                Transactions
              </Heading>
            </Row>
          </View>

          {error && (
            <View style={{ paddingHorizontal: theme.spacing.md }}>
              <ModernErrorMessage message={error} />
              <Spacer size="md" />
            </View>
          )}

          {/* Transaction List */}
          <View style={{ paddingHorizontal: theme.spacing.md }}>
            {transactions.length === 0 ? (
              <Column align="center" justify="center" style={{ paddingVertical: theme.spacing.xl }}>
                <Ionicons
                  name="receipt-outline"
                  size={64}
                  color={theme.colors.neutral[400]}
                />
                <Spacer size="lg" />
                <Heading level="h3" style={{ color: theme.colors.brandNavy, textAlign: 'center' }}>
                  No Transactions
                </Heading>
                <Spacer size="sm" />
                <Text style={{ color: theme.colors.neutral[600], textAlign: 'center' }}>
                  {hasActiveSubscription 
                    ? "Your transaction history will appear here."
                    : "Subscribe to a plan to see your transaction history."
                  }
                </Text>
              </Column>
            ) : (
              <>
                <Text
                  variant="caption"
                  weight="medium"
                  style={{
                    color: theme.colors.neutral[600],
                    marginBottom: theme.spacing.md,
                  }}
                >
                  TRANSACTION HISTORY
                </Text>
                <FlatList
                  data={transactions}
                  renderItem={renderTransaction}
                  keyExtractor={(item) => item.id}
                  scrollEnabled={false}
                  showsVerticalScrollIndicator={false}
                />
              </>
            )}
          </View>

          {/* Bottom Padding for Floating Tab Bar */}
          <View style={{ height: 100 }} />
        </ScrollView>
      </Container>
    </SafeAreaWrapper>
  );
};

export default TransactionsPage;
