import React, { useState, useEffect } from "react";
import {
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  FlatList,
} from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "../../../shared/context/ThemeContext";
import { useSubscription } from "../../../shared/hooks/useSubscription";
import SafeAreaWrapper from "../../../shared/components/SafeAreaWrapper";
import { subscriptionService } from "../../../shared/services/subscriptionService";

// Import modern components
import {
  Container,
  Row,
  Column,
  Spacer,
  Text,
  Heading,
  ModernCard,
  ModernErrorMessage,
} from "../../../shared/components";

/**
 * Transactions page component
 * Shows transaction history with dates, amounts, and status
 */
const TransactionsPage = () => {
  const router = useRouter();
  const { theme } = useTheme();
  
  // Subscription hook
  const {
    hasActiveSubscription,
    loading: subscriptionLoading,
    refreshSubscription,
  } = useSubscription();

  // State
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch transactions
  const fetchTransactions = async () => {
    try {
      setError(null);
      console.log('🔄 Fetching transaction history...');

      const response = await subscriptionService.getTransactions({
        page: 1,
        limit: 20
      });

      if (response.success && response.data) {
        const formattedTransactions = response.data.transactions.map(transaction => ({
          id: transaction._id,
          date: new Date(transaction.createdAt),
          amount: Math.abs(transaction.amount), // Use absolute value for display
          currency: transaction.currency,
          status: transaction.status,
          description: getTransactionDescription(transaction),
          paymentMethod: getPaymentMethod(transaction),
          type: transaction.type,
          razorpayPaymentId: transaction.razorpayPaymentId,
          razorpayOrderId: transaction.razorpayOrderId,
        }));

        setTransactions(formattedTransactions);
        console.log('✅ Transactions loaded:', formattedTransactions.length);
      } else {
        console.warn('⚠️ No transaction data received');
        setTransactions([]);
      }
    } catch (err) {
      console.error('❌ Error fetching transactions:', err);
      setError('Failed to load transaction history');
      setTransactions([]);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to get transaction description
  const getTransactionDescription = (transaction) => {
    if (transaction.type === 'refund') {
      return `Refund - ${transaction.planType} ${transaction.planDuration}`;
    }
    if (transaction.type === 'subscription_upgrade') {
      return `Upgrade to ${transaction.planType} ${transaction.planDuration}`;
    }
    return `${transaction.planType} ${transaction.planDuration} Subscription`;
  };

  // Helper function to get payment method
  const getPaymentMethod = (transaction) => {
    if (transaction.razorpayPaymentId) {
      // Extract last 4 digits from payment ID for display
      const paymentId = transaction.razorpayPaymentId;
      const last4 = paymentId.slice(-4);
      return `Payment ID ending in ${last4}`;
    }
    return 'Razorpay Payment';
  };

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([
        refreshSubscription(),
        fetchTransactions(),
      ]);
      console.log('✅ Transactions refreshed successfully');
    } catch (error) {
      console.error('❌ Error refreshing transactions:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return theme.colors.brandGreen;
      case 'failed':
        return theme.colors.error[500];
      case 'pending':
        return theme.colors.warning.main;
      default:
        return theme.colors.neutral[500];
    }
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return 'checkmark-circle';
      case 'failed':
        return 'close-circle';
      case 'pending':
        return 'time';
      default:
        return 'help-circle';
    }
  };

  // Render transaction item
  const renderTransaction = ({ item }) => (
    <ModernCard
      style={{
        backgroundColor: theme.colors.brandWhite,
        borderRadius: 16,
        padding: theme.spacing.lg,
        marginBottom: theme.spacing.md,
        borderWidth: 1,
        borderColor: theme.colors.neutral[100],
      }}
    >
      <Row justify="space-between" align="flex-start">
        {/* Transaction Icon */}
        <View
          style={{
            width: 48,
            height: 48,
            borderRadius: 24,
            backgroundColor: getStatusColor(item.status) + '15',
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: theme.spacing.md,
          }}
        >
          <Ionicons
            name={getStatusIcon(item.status)}
            size={24}
            color={getStatusColor(item.status)}
          />
        </View>

        {/* Transaction Details */}
        <Column style={{ flex: 1 }}>
          <Text
            weight="semibold"
            style={{
              color: theme.colors.brandNavy,
              fontSize: 16,
              marginBottom: 4,
            }}
          >
            {item.description}
          </Text>
          <Text
            variant="caption"
            style={{
              color: theme.colors.neutral[600],
              marginBottom: 6,
            }}
          >
            {item.date.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </Text>
          <Row align="center">
            <View
              style={{
                backgroundColor: getStatusColor(item.status) + '20',
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 12,
              }}
            >
              <Text
                variant="caption"
                weight="medium"
                style={{
                  color: getStatusColor(item.status),
                  textTransform: 'capitalize',
                  fontSize: 11,
                }}
              >
                {item.status}
              </Text>
            </View>
          </Row>
        </Column>

        {/* Amount */}
        <Column align="flex-end">
          <Text
            weight="bold"
            style={{
              color: item.type === 'refund' ? theme.colors.error : theme.colors.brandNavy,
              fontSize: 18,
            }}
          >
            {item.type === 'refund' ? '-' : ''}{item.currency === 'INR' ? '₹' : '$'}{item.amount}
          </Text>
          <Text
            variant="caption"
            style={{
              color: theme.colors.neutral[500],
              marginTop: 2,
            }}
          >
            {item.currency}
          </Text>
        </Column>
      </Row>
    </ModernCard>
  );

  useEffect(() => {
    fetchTransactions();
  }, []);

  if (subscriptionLoading || loading) {
    return (
      <SafeAreaWrapper>
        <Container withPadding>
          <Column align="center" justify="center" style={{ flex: 1 }}>
            <ActivityIndicator size="large" color={theme.colors.primary[500]} />
            <Spacer size="md" />
            <Text>Loading transactions...</Text>
          </Column>
        </Container>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <Container>
        <ScrollView
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.brandGreen]}
              tintColor={theme.colors.brandGreen}
            />
          }
        >
          {/* Header */}
          <View style={{ padding: theme.spacing.md }}>
            <Row align="center" style={{ marginBottom: theme.spacing.lg }}>
              <Heading level="h2" style={{ color: theme.colors.brandNavy }}>
                Transactions
              </Heading>
            </Row>
          </View>

          {error && (
            <View style={{ paddingHorizontal: theme.spacing.md }}>
              <ModernErrorMessage message={error} />
              <Spacer size="md" />
            </View>
          )}

          {/* Transaction List */}
          <View style={{ paddingHorizontal: theme.spacing.md }}>
            {transactions.length === 0 ? (
              <Column align="center" justify="center" style={{ paddingVertical: theme.spacing.xl }}>
                <Ionicons
                  name="receipt-outline"
                  size={64}
                  color={theme.colors.neutral[400]}
                />
                <Spacer size="lg" />
                <Heading level="h3" style={{ color: theme.colors.brandNavy, textAlign: 'center' }}>
                  No Transactions
                </Heading>
                <Spacer size="sm" />
                <Text style={{ color: theme.colors.neutral[600], textAlign: 'center' }}>
                  {hasActiveSubscription 
                    ? "Your transaction history will appear here."
                    : "Subscribe to a plan to see your transaction history."
                  }
                </Text>
              </Column>
            ) : (
              <>
                <Text
                  variant="caption"
                  weight="medium"
                  style={{
                    color: theme.colors.neutral[600],
                    marginBottom: theme.spacing.md,
                  }}
                >
                  TRANSACTION HISTORY
                </Text>
                <FlatList
                  data={transactions}
                  renderItem={renderTransaction}
                  keyExtractor={(item) => item.id}
                  scrollEnabled={false}
                  showsVerticalScrollIndicator={false}
                />
              </>
            )}
          </View>

          {/* Bottom Padding for Floating Tab Bar */}
          <View style={{ height: 100 }} />
        </ScrollView>
      </Container>
    </SafeAreaWrapper>
  );
};

export default TransactionsPage;
