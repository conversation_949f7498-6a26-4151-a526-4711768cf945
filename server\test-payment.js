/**
 * Test script for Razorpay payment system
 * This script tests the basic functionality of the payment system
 */

import fetch from 'node-fetch';
import config from './src/config/index.js';

const BASE_URL = `http://localhost:${config.server?.port || process.env.PORT || 5001}`;

// Test user credentials (you should create a test user first)
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

let authToken = null;

/**
 * Test authentication (using OTP flow)
 */
async function testAuth() {
  console.log('🔐 Testing authentication...');

  try {
    // Step 1: Send OTP
    console.log('📧 Sending OTP...');
    const otpResponse = await fetch(`${BASE_URL}/api/v1/auth/send-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email: TEST_USER.email })
    });

    const otpData = await otpResponse.json();

    if (!otpData.success) {
      console.log('❌ Failed to send OTP:', otpData.message);
      return false;
    }

    console.log('✅ OTP sent successfully');

    // For testing, we'll skip the actual OTP verification
    // In a real scenario, you would need to get the OTP from email/SMS
    console.log('⚠️ Skipping OTP verification for testing');
    console.log('✅ Authentication flow tested (OTP-based)');

    // For now, we'll create a mock token for testing other endpoints
    // In production, you would get this from the verify-otp endpoint
    authToken = 'mock-token-for-testing';
    return true;

  } catch (error) {
    console.error('❌ Authentication error:', error.message);
    return false;
  }
}

/**
 * Test getting subscription plans
 */
async function testGetPlans() {
  console.log('📋 Testing subscription plans...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/v1/subscriptions/plans`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Plans retrieved successfully');
      console.log('Available plans:', data.data.map(p => `${p.name} - ₹${p.price}`));
      return data.data;
    } else {
      console.log('❌ Failed to get plans:', data.message);
      return null;
    }
  } catch (error) {
    console.error('❌ Plans error:', error.message);
    return null;
  }
}

/**
 * Test creating a subscription order
 */
async function testCreateOrder(planId) {
  console.log('💳 Testing order creation...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/v1/subscriptions/create-order`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        planId: planId
      })
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Order created successfully');
      console.log('Order ID:', data.data.orderId);
      console.log('Payment URL:', data.data.paymentUrl);
      return data.data;
    } else {
      console.log('❌ Failed to create order:', data.message);
      return null;
    }
  } catch (error) {
    console.error('❌ Order creation error:', error.message);
    return null;
  }
}

/**
 * Test getting current subscription
 */
async function testCurrentSubscription() {
  console.log('📊 Testing current subscription...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/v1/subscriptions/current`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Current subscription retrieved');
      if (data.data) {
        console.log('Subscription status:', data.data.status);
        console.log('Plan:', data.data.planId);
      } else {
        console.log('No active subscription');
      }
      return data.data;
    } else {
      console.log('❌ Failed to get current subscription:', data.message);
      return null;
    }
  } catch (error) {
    console.error('❌ Current subscription error:', error.message);
    return null;
  }
}

/**
 * Test payment configuration
 */
async function testPaymentConfig() {
  console.log('⚙️ Testing payment configuration...');
  
  // Check if Razorpay credentials are set
  if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_KEY_SECRET) {
    console.log('❌ Razorpay credentials not configured');
    return false;
  }
  
  console.log('✅ Razorpay credentials configured');
  console.log('Key ID:', process.env.RAZORPAY_KEY_ID);
  
  // Check if payments are enabled
  if (process.env.ENABLE_PAYMENTS !== 'true') {
    console.log('❌ Payments not enabled');
    return false;
  }
  
  console.log('✅ Payments enabled');
  return true;
}

/**
 * Test Razorpay SDK integration
 */
async function testRazorpaySDK() {
  console.log('🔧 Testing Razorpay SDK integration...');

  try {
    // Import the gateway
    const { default: RazorpayGateway } = await import('./src/services/payment/gateways/RazorpayGateway.js');
    const gateway = new RazorpayGateway();

    // Test creating an order
    const amount = 100; // ₹1.00 for testing (in paise)
    const currency = 'INR';
    const options = {
      receipt: 'test_receipt_' + Date.now()
    };

    const result = await gateway.createOrder(amount, currency, options);

    if (result && result.success && result.data) {
      console.log('✅ Razorpay order creation successful');
      console.log('Order ID:', result.data.id);
      console.log('Amount:', result.data.amount / 100, 'INR');
      return true;
    } else {
      console.log('❌ Failed to create Razorpay order');
      console.log('Error:', result?.error || 'Unknown error');
      return false;
    }
  } catch (error) {
    console.error('❌ Razorpay SDK error:', error.message);
    return false;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('🚀 Starting Razorpay payment system tests...\n');

  // Test payment configuration
  const configOk = await testPaymentConfig();
  if (!configOk) {
    console.log('\n❌ Payment configuration test failed. Please check your environment variables.');
    return;
  }

  console.log('');

  // Test Razorpay SDK
  const sdkOk = await testRazorpaySDK();
  if (!sdkOk) {
    console.log('\n❌ Razorpay SDK test failed.');
    return;
  }

  console.log('');

  // Test authentication flow
  const authOk = await testAuth();
  if (!authOk) {
    console.log('\n❌ Authentication test failed.');
    return;
  }

  console.log('\n✅ Payment system configuration tests completed successfully!');
  console.log('\n📝 Summary:');
  console.log('✅ Razorpay credentials configured');
  console.log('✅ Payment features enabled');
  console.log('✅ Razorpay SDK working');
  console.log('✅ Authentication flow available');
  console.log('\n📝 Next steps for full testing:');
  console.log('1. Create a test user account through the app');
  console.log('2. Test the complete payment flow with a real user session');
  console.log('3. Test webhook processing with Razorpay test events');
  console.log('4. Verify subscription activation and management');
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(console.error);
}

export { runTests, testAuth, testGetPlans, testCreateOrder, testCurrentSubscription };
