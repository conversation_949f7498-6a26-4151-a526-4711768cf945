{"name": "unextdoor-server", "version": "1.0.0", "description": "Backend server for UNextDoor app", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1", "migrate:achievements": "node src/scripts/migrateAchievements.js", "migrate:achievements:force": "node src/scripts/migrateAchievements.js --force", "migrate:users": "node src/scripts/migrateAchievements.js --users-only", "migrate:init": "node src/scripts/migrateAchievements.js --achievements-only"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@openai/realtime-api-beta": "github:openai/openai-realtime-api-beta", "@vapi-ai/server-sdk": "^0.7.0", "base-64": "^1.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.5.0", "express": "^5.1.0", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.2", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^7.0.3", "openai": "^4.100.0", "razorpay": "^2.9.2", "socket.io": "^4.8.1", "uuid": "^9.0.1", "ws": "^8.18.2"}, "devDependencies": {"nodemon": "^3.1.10"}}