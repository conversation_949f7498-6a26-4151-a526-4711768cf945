import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../features/auth/context/AuthContext';
import { SubscriptionService } from '../services/subscriptionService';

export const useSubscription = () => {
  const { token } = useAuth();
  const [subscription, setSubscription] = useState(null);
  const [features, setFeatures] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchSubscriptionData = useCallback(async () => {
    if (!token) {
      setLoading(false);
      return;
    }

    try {
      setError(null);

      // Fetch current subscription
      const subscriptionData = await SubscriptionService.getCurrentSubscription();

      // Fetch user features and usage
      const featuresData = await SubscriptionService.getUserFeatures();

      if (subscriptionData.success) {
        setSubscription(subscriptionData.data);
      }

      if (featuresData.success) {
        setFeatures(featuresData.data);
      }

    } catch (err) {
      console.error('Error fetching subscription data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [token]);

  const refreshSubscription = useCallback(() => {
    setLoading(true);
    fetchSubscriptionData();
  }, [fetchSubscriptionData]);

  // Get current plan information
  const getCurrentPlan = useCallback(() => {
    if (!subscription?.subscription) {
      return {
        id: 'free',
        name: 'Free',
        tier: 'free',
        isActive: false,
        price: 0,
        interval: null,
      };
    }

    const sub = subscription.subscription;
    let tier = 'free';
    let name = 'Free';

    if (sub.planId === 'basic_monthly') {
      tier = 'basic';
      name = 'Basic';
    } else if (sub.planId === 'standard_quarterly') {
      tier = 'standard';
      name = 'Standard';
    } else if (sub.planId === 'pro_yearly') {
      tier = 'pro';
      name = 'Pro';
    }

    return {
      id: sub.planId,
      name,
      tier,
      isActive: subscription.hasActiveSubscription,
      price: sub.amount,
      interval: sub.interval,
      intervalCount: sub.intervalCount,
      currentPeriodEnd: sub.currentPeriodEnd,
      cancelAtPeriodEnd: sub.cancelAtPeriodEnd,
    };
  }, [subscription]);

  // Get usage information
  const getUsage = useCallback(() => {
    if (!features) {
      return {
        lessons: { current: 0, limit: 5, remaining: 5 },
        aiSessions: { current: 0, limit: 15, remaining: 15 },
        vocabulary: { current: 0, limit: -1, remaining: -1 },
      };
    }

    const lessonUsage = features.usage.lessons || {};
    const currentPlan = getCurrentPlan();

    // AI sessions usage (simulated for now - TODO: Implement actual tracking)
    const aiSessionsLimit = currentPlan.tier === 'free' ? 15 : -1;
    const aiSessionsCurrent = 0; // TODO: Implement AI sessions tracking

    return {
      lessons: {
        current: lessonUsage.currentCount || 0,
        limit: currentPlan.tier === 'free' ? 5 : -1,
        remaining: currentPlan.tier === 'free' 
          ? Math.max(0, 5 - (lessonUsage.currentCount || 0))
          : -1,
      },
      aiSessions: {
        current: aiSessionsCurrent,
        limit: aiSessionsLimit,
        remaining: aiSessionsLimit === -1 ? -1 : Math.max(0, aiSessionsLimit - aiSessionsCurrent),
      },
      vocabulary: {
        current: 0, // No vocabulary limits for any plan
        limit: -1,
        remaining: -1,
      },
    };
  }, [features, getCurrentPlan]);

  // Check if user can access a feature
  const canAccess = useCallback((featureName) => {
    if (!features?.features?.available) {
      return false;
    }

    const feature = features.features.available[featureName];
    return feature?.hasAccess || false;
  }, [features]);

  // Check if user has reached usage limit
  const hasReachedLimit = useCallback((featureName) => {
    const usage = getUsage();
    const featureUsage = usage[featureName];
    
    if (!featureUsage || featureUsage.limit === -1) {
      return false; // Unlimited
    }

    return featureUsage.current >= featureUsage.limit;
  }, [getUsage]);

  useEffect(() => {
    fetchSubscriptionData();
  }, [fetchSubscriptionData]);

  return {
    subscription: subscription?.subscription,
    hasActiveSubscription: subscription?.hasActiveSubscription || false,
    currentPlan: getCurrentPlan(),
    usage: getUsage(),
    features,
    loading,
    error,
    canAccess,
    hasReachedLimit,
    refreshSubscription,
  };
};
